#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_pdf_view.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 调解协议PDF下载视图
"""

import logging
from rest_framework.generics import GenericAPIView
from rest_framework import serializers

from apps.mediation_management.models import MediationCase
from utils.ajax_result import AjaxResult
from utils.file_security_helper import FileSecurityHelper
from utils.permission_helper import MyPermission, WechatFaceAuthPermission

# 获取日志记录器
logger = logging.getLogger(__name__)


class MediationAgreementPDFSerializer(serializers.Serializer):
    """
    调解协议PDF下载接口的序列化器，用于API文档生成。
    """
    pass


class MediationAgreementPDFView(GenericAPIView):
    """
    调解协议PDF下载视图

    提供调解协议PDF文件的查找和安全下载功能。该视图通过调解案件号查找已生成的PDF文件，
    并提供安全的下载链接。PDF文件在调解方案配置或电子签名更新时自动生成并存储到
    调解案件的attachments关系中。
    """

    # 配置序列化器类用于API文档生成
    serializer_class = MediationAgreementPDFSerializer

    # 配置权限类：需要用户认证
    permission_classes = [MyPermission | WechatFaceAuthPermission]  # 可根据需要调整权限

    def get(self, request, case_number):
        """
        获取调解协议PDF文件并提供下载链接

        根据调解案件号查找已生成的调解协议PDF文件，并返回安全的下载链接。
        PDF文件在调解方案配置或电子签名更新时自动生成。

        **请求参数：**

        **路径参数：**
        - case_number (字符串, 必需): 调解案件号，通过URL路径参数传递

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/wechat/GZTJ20250801ABC123/agreement_pdf/
        ```

        **响应数据结构：**

        成功响应：
        ```json
        {
            "code": 200,
            "msg": "PDF文件获取成功",
            "state": "success",
            "data": {
                "download_url": "/user/files/download/550e8400-e29b-41d4-a716-446655440000/",
                "file_name": "调解协议_GZTJ20250801ABC123.pdf",
                "file_size": 1024000
            }
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "调解案件不存在",
            "state": "fail"
        }
        ```
        ```json
        {
            "code": 400,
            "msg": "PDF文件尚未生成，请先完成调解方案配置和电子签名",
            "state": "fail"
        }
        ```
        """
        try:
            # 记录请求日志
            logger.info(f"开始查找调解协议PDF，案件号: {case_number}")

            # 通过案件号查询调解案件
            try:
                mediation_case = MediationCase.objects.get(case_number=case_number)
            except MediationCase.DoesNotExist:
                logger.warning(f"调解案件不存在，案件号: {case_number}")
                return AjaxResult.not_found(msg="调解案件不存在")

            # 从已有附件中查找PDF文件
            file_name = f"调解协议_{case_number}.pdf"
            existing_pdf_files = mediation_case.attachments.filter(file_name=file_name)

            if not existing_pdf_files.exists():
                logger.warning(f"调解协议PDF文件不存在，案件号: {case_number}")
                return AjaxResult.fail(msg="PDF文件尚未生成，请先完成调解方案配置和电子签名")

            # 获取最新的PDF文件（如果有多个，取最新的）
            case_file = existing_pdf_files.order_by('-created_time').first()

            # 使用FileSecurityHelper生成安全下载链接
            download_url = FileSecurityHelper.generate_secure_download_url(case_file)
            if not download_url:
                logger.warning(f"无法生成安全下载链接，案件号: {case_number}")
                return AjaxResult.fail(msg="无法生成安全下载链接")

            # 构建响应数据
            response_data = {
                "download_url": download_url,
                "file_name": file_name,
                "file_size": case_file.file.size if case_file.file else 0,
                "secure_token": str(case_file.secure_token) if case_file.secure_token else None,
            }

            logger.info(f"调解协议PDF查找成功，案件号: {case_number}, 文件大小: {case_file.file.size if case_file.file else 0} bytes")
            return AjaxResult.success(msg="PDF文件获取成功", data=response_data)

        except serializers.ValidationError as e:
            # 参数验证失败
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except Exception as e:
            logger.error(f"获取调解协议PDF时发生异常: {str(e)}")
            return AjaxResult.fail(msg="PDF文件获取失败")
